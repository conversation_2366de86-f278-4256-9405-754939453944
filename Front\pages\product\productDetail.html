<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电源网</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/jq.js"></script>
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/productImg.css">
    <!-- 综合-script -->
    <script src="../../script/index.js" defer></script>
     <!-- 产品分类模块 -->
     <script src="../../script/productCategory.js" defer></script>
    <!-- 所有页面组成部分 -->
    <script src="../../components/common.js" defer></script>
    <script src="../../components/myTable.js" defer></script>
    <!-- 单页面组成部分 -->
    <script src="./components/page.js" defer></script>
</head>

<body>
   <!-- 引入头部组件 -->
   <div id="header"></div>

    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">产品分类1</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">产品分类2</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">产品分类3</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">产品分类4</div>
        </div>
        <div class="productDetail">
            <div class="productBox">
                <!-- 第一个块：产品图片 -->
                <div class="product-block image-block">
                    <div class="product-image">
                        <img src="../../images/power/3w5.png" alt="">
                    </div>
                    <div class="image-note">*图片仅供参考，具体参数请以实物/技术规格书为准</div>
                </div>

                <!-- 第二个块：产品信息 -->
                <div class="product-block info-block">
                    <div class="product-series">2W 系列</div>
                    <p class="spec-label clo">型号:<span > HLK-2M03</span></p>
                    <p class="spec-label">功率: <span > 2W</span></p>
                    <p class="spec-label">输入电压: <span > 90-265VAC (120-350VDC)</span></p>
                    <p class="spec-label">输出电压(VDC):<span > 3.3V</span></p>
                    <p class="spec-label">型号:<span > HLK-2M03</span></p>
                    <p class="spec-label">功率: <span > 2W</span></p>
                    <p class="spec-label">输入电压: <span > 90-265VAC (120-350VDC)</span></p>
                    <p class="spec-label">输出电压(VDC):<span > 3.3V</span></p>
                </div>

                <!-- 第三个块：详细参数和操作 -->
                <div class="product-block action-block">
                    <div class="product-actions">
                        <button class="selection-btn">技术手册</button>
                        <button class="action-btn apply-btn">申请样品</button>
                    </div>
                </div>
            </div>

            <div class="product-selection">
                <p>找不到所需产品？请移步"产品选型"中心，您可以通过参数筛选查找您所需的产品</p>
                <button class="selection-btn">产品选型</button>
            </div>
        </div>
    </div>

    <div class="bug"></div>

    <!-- 引入底部组件 -->
    <div id="footer"></div>
</body>
<script>

</script>

</html>
