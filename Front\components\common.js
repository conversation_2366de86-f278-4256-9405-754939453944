document.addEventListener('DOMContentLoaded', function() {
    // 加载头部组件
    const headerElement = document.getElementById('header');
    if (headerElement) {
        fetch('../../components/header.html')
            .then(response => response.text())
            .then(data => {
                headerElement.innerHTML = data;
                // 头部组件加载完成后，初始化产品分类模块
                if (typeof window.reinitProductCategory === 'function') {
                    // console.log('头部组件加载完成，重新初始化产品分类模块');
                    window.reinitProductCategory();
                }
            })
            .catch(error => {
                console.error('加载头部组件出错:', error);
            });
    }

    // 加载底部组件
    const footerElement = document.getElementById('footer');
    if (footerElement) {
        fetch('../../components/footer.html')
            .then(response => response.text())
            .then(data => {
                footerElement.innerHTML = data;
            })
            .catch(error => {
                console.error('加载底部组件出错:', error);
            });
    }

});