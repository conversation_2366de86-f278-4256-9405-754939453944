
function addBom(orderId = "S02412069039") {
  var title = orderId + ' --BOM标识'
  layui.layer.open({
    type: 1,
    // area: size,
    skin: 'dialog_addBom',
    resize:true,
    shadeClose: true,
    title,
    btn: ['保存'],
    btn1: function (index, layero) {
        layer.close(index)
        console.log(index,layero);
        return false;
    },
    content: `
    <style>
    .bomBox{
      min-width: 400px;
      padding: 1vw 1vw .5vw 1vw;
  }
  .bomBox>div{
      margin-bottom: .5vw;
      font-size: .75vw;
      letter-spacing: 1px;
  }
  /* bomBox 自定义按钮 */
  .dialog_addBom>.layui-layer-btn{
      width: 100%;
      padding: 0px;
      margin: 0px;
      box-sizing: border-box;
      /* border: 2px solid ; */
      margin-bottom: 1vw;
  }
  .dialog_addBom>.layui-layer-btn>a{
      width: 60%;
      margin-right: 20%;
      min-height: 32px;
      text-align: center;
      background-color: var(--blue-deep);
  }
  </style>
    <div class="bomBox">
        <div class="bomTip">您对此订单添加备注，是方便您自己辨识和日后查找</div>
        <div class="bomTip" style="color:crimson;">注意:此备注不是给我商城工作人员的备注，千万别误解.</div>
        <div class="textarea" data-currentCount="0" data-maxLength="/20"
            style="position: relative;">
            <textarea name="" maxlength="20" rows="2" oninput="textareaOninput(this)"
                placeholder="请输入BOM标识备注：(20个字符以内)" class="layui-textarea"
                style="padding: .3vw .3vw 1.5vw .5vw;resize: none;font-size: .8vw;letter-spacing: 1px;"></textarea>
        </div>
       
    </div>
    `
  })
  //  <button class="button button_blue" style="width:60%;margin-left:20%;margin-top:.5vw;" onclick="bomConfirm(${1})">保存</button> 
}

// function bomConfirm(closeIndex) {
//     // console.log(closeIndex);
//     layui.layer.closeAll()
// }

