﻿using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers
{
    /// <summary>
    /// 问题管理
    /// </summary>
    [DisplayName("问题管理")]
    [Description("用于问题的管理")]
    [AdminArea]
    [DHMenu(90, ParentMenuName = "Sites", ParentMenuDisplayName = "网站", ParentMenuUrl = "~/{area}/Sites", ParentMenuOrder = 85, ParentVisible = true, CurrentMenuUrl = "~/{area}/Problem", CurrentMenuName = "Problem", CurrentIcon = "&#xe652;", LastUpdate = "20250603")]
    public class ProblemController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("列表")]
        public IActionResult Index(String name, Int32 page,Int32 problemType = -1,Int32 status = -1)
        {
            dynamic viewModel = new ExpandoObject();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                Sort = Entity.Problem._.CreateTime,
                Desc = true,
                RetrieveTotalCount = true
            };

            var list = Entity.Problem.Search(status, problemType, null, name, pages);

            viewModel.list = list;
            viewModel.name = name;
            viewModel.status = status;
            viewModel.problemType = problemType;
            viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("编辑")]
        public IActionResult Update(Int32 Id)
        {
            var model = Entity.Problem.FindById(Id);
            if (model == null) return Content(GetResource("问题不存在"));
            return View(model);
        }

        [HttpPost]
        public IActionResult Update([FromForm]Int32 Id, [FromForm]String answer)
        {
            if (answer.IsNullOrWhiteSpace()) return Prompt(new PromptModel { Message = GetResource("回复不能为空"), IsOk = false });
            var model = Entity.Problem.FindById(Id);
            if (model == null) return Prompt(new PromptModel { Message = GetResource("问题不存在"), IsOk = false });
            model.Answer = answer;
            model.Update();
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除")]
        public IActionResult Delete(String Ids)
        {
            Entity.Problem.Delete(Entity.Problem._.Id.In(Ids.Trim(',')));
            return Json(new { success = true });
        }

        /// <summary>
        /// 修改显示状态
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateShow(Int32 Id)
        {
            var model = Entity.Problem.FindById(Id);
            if (model == null) return Json(new { success = false, msg = GetResource("问题不存在") });
            model.Show = !model.Show;
            model.Update();
            return Json(new { success = true });
        }
    }
}
