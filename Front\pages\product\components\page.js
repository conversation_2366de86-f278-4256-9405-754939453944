document.addEventListener('DOMContentLoaded', function() {
  // console.log('开始加载series-grid.html');
  const seriesGridElement = document.querySelector('.series-grid');
  if (seriesGridElement) {
    fetch('../../pages/product/components/seriesGrid.html')
    .then(response => {
      console.log('fetch响应状态:', response.status);
      if (!response.ok) {
        throw new Error('网络响应不正常，状态码: ' + response.status);
      }
      return response.text();
    })
    .then(data => {
      console.log('获取到的数据:', data.substring(0, 100) + '...');
      seriesGridElement.innerHTML = data;
      console.log('已将内容插入到series-grid元素中');
    })
    .catch(error => {
      console.error('加载series-grid.html时出错:', error);
      // 加载失败时显示错误信息
      document.querySelector('.series-grid').innerHTML = 
        '<p style="color:red">加载内容失败，请检查网络连接或文件路径</p>';
    });
    
  // 检查series-grid元素是否存在
  console.log('series-grid元素:', document.querySelector('.series-grid'));
    
  }
});
