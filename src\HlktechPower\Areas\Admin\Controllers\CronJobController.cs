﻿using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Cube.Services;
using NewLife.Data;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers;

/// <summary>定时作业管理</summary>
[DisplayName("定时作业")]
[Description("定时作业管理")]
[AdminArea]
[DHMenu(70, ParentMenuName = "Settings", CurrentMenuUrl = "~/{area}/CronJob", CurrentMenuName = "CronJobList", CurrentIcon = "&#xe71f;", LastUpdate = "20250527")]
public class CronJobController : PekCubeAdminControllerX {
    /// <summary>
    /// 定时作业管理
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("定时作业管理")]
    public IActionResult Index(String key, Int32 page = 1, Int32 limit = 10)
    {
        dynamic viewModel = new ExpandoObject();

        key = key.SafeString().Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = CronJob._.Id,
            Desc = true,
        };

        viewModel.list = CronJob.Search(key, pages);
        viewModel.key = key;
        viewModel.page = page;
        viewModel.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "key", key } });

        return View(viewModel);
    }

    /// <summary>
    /// 新增定时作业
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增定时作业")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>
    /// 新增定时作业
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增定时作业")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult Add(String Name, String DisplayName, String Cron, String Method, String Argument, Boolean Enable, String Remark)
    {
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("作业名称不能为空") });
        }

        if (DisplayName.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("显示名不能为空") });
        }

        if (Cron.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("Cron表达式不能为空") });
        }

        if (Method.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("命令方法全名不能为空") });
        }

        var model = CronJob.FindByName(Name);
        if (model != null)
        {
            return Prompt(new PromptModel { Message = GetResource("作业名称不能重复") });
        }

        model = new CronJob();
        model.Name = Name;
        model.DisplayName = DisplayName;
        model.Cron = Cron;
        model.Method = Method;
        model.Argument = Argument;
        model.Enable = Enable;
        model.Remark = Remark;

        var next = DateTime.MinValue;
        foreach (var item in model.Cron.Split(";"))
        {
            var cron = new NewLife.Threading.Cron();
            if (!cron.Parse(item)) throw new ArgumentException("Cron表达式有误！", nameof(model.Cron));

            var dt = cron.GetNext(DateTime.Now);
            if (next == DateTime.MinValue || dt < next) next = dt;
        }

        // 重算下一次的时间
        if (model is IEntity e && !e.Dirtys[nameof(model.Name)]) model.NextTime = next;
        JobService.Wake();

        model.Insert();

        return Prompt(new PromptModel { Message = GetResource("新增成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 编辑定时作业
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑定时作业")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 Id)
    {
        var model = CronJob.FindById(Id);

        if (model == null)
        {
            return Content(GetResource("作业不存在"));
        }

        return View(model);
    }

    /// <summary>
    /// 编辑定时作业
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑定时作业")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult Edit(Int32 Id, String Name, String DisplayName, String Cron, String Method, String Argument, Boolean Enable, String Remark)
    {
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("作业名称不能为空") });
        }

        if (DisplayName.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("显示名不能为空") });
        }

        if (Cron.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("Cron表达式不能为空") });
        }

        if (Method.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("命令方法全名不能为空") });
        }

        var model = CronJob.FindByName(Name);
        if (model != null && model.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("作业名称不能重复") });
        }

        model = CronJob.FindById(Id);
        if (model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("作业不存在") });
        }

        model.Name = Name;
        model.DisplayName = DisplayName;
        model.Cron = Cron;
        model.Method = Method;
        model.Argument = Argument;
        model.Enable = Enable;
        model.Remark = Remark;

        var next = DateTime.MinValue;
        foreach (var item in model.Cron.Split(";"))
        {
            var cron = new NewLife.Threading.Cron();
            if (!cron.Parse(item)) throw new ArgumentException("Cron表达式有误！", nameof(model.Cron));

            var dt = cron.GetNext(DateTime.Now);
            if (next == DateTime.MinValue || dt < next) next = dt;
        }

        // 重算下一次的时间
        if (model is IEntity e && !e.Dirtys[nameof(model.Name)]) model.NextTime = next;
        JobService.Wake();

        model.Update();

        return Prompt(new PromptModel { Message = GetResource("编辑成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 删除定时作业
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除定时作业")]
    public IActionResult Delete(String Ids)
    {
        var res = new DResult();

        CronJob.Delete(CronJob._.Id.In(Ids.Trim(',')));
        CronJob.Meta.Cache.Clear("");

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 立即执行
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("立即执行")]
    [HttpGet]
    public IActionResult ExecuteNow(Int32 Id)
    {
        var res = new DResult();

        var entity = CronJob.FindById(Id);
        if (entity != null && entity.Enable)
        {
            entity.NextTime = DateTime.Now;
            entity.Update();

            JobService.Wake(entity.Id, -1);
        }

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("已安排执行");
        return Json(res);
    }

    /// <summary>修改状态</summary>
    /// <returns></returns>
    [DisplayName("修改状态")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult ModifyState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var model = CronJob.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        model.Enable = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }
}
