﻿using DH.Core.Domain.Localization;
using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using NewLife.Log;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;
using System.ComponentModel;
using System.Dynamic;
using System.Reflection.Emit;
using System.Xml.Linq;
using XCode;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers
{
    /// <summary>
    /// 资料管理
    /// </summary>
    [DisplayName("资料管理")]
    [Description("用于资料文件的管理")]
    [AdminArea]
    [DHMenu(50, ParentMenuName = "Product", ParentMenuDisplayName = "产品", ParentMenuUrl = "~/{area}/Product", ParentMenuOrder = 90, ParentVisible = true, CurrentMenuUrl = "~/{area}/DataFile", CurrentMenuName = "DataFile", CurrentIcon = "&#xe652;", LastUpdate = "20250530")]
    public class DataFileController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("列表")]
        public IActionResult Index(Int32 page,String name,Int32 classVal = -1)
        {
            dynamic viewModel = new ExpandoObject();

            var list = DataFile.FindAll().WhereIf(classVal >= 0, e => e.DataFileClass == classVal).Select(e => new DataFile
            {
                Id = e.Id,
                Name = DataFileLan.FindByDataFile(e, WorkingLanguage.Id).Name,
                Show = e.Show,
                Sort = e.Sort,
                DataFileClass = e.DataFileClass,
                DownloadVolume = e.DownloadVolume,
            }).OrderBy(e => e.Sort).WhereIf(name.IsNotNullAndWhiteSpace(), e => e.Name != null && e.Name.Contains(name)).ToList();

            viewModel.list = list.Skip((page - 1) * 10).Take(10).ToList();

            var pageCount = list.Count / 10;

            if (pageCount == 0) pageCount = 1;
            else if (list.Count % 10 > 0) pageCount++;

            viewModel.name = name;
            viewModel.classVal = classVal;
            viewModel.PageHtml = PageHelper.CreatePage(page, list.Count, pageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Insert)]
        [DisplayName("添加")]
        public IActionResult Add()
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            return View(viewModel);
        }

        [HttpPost]
        public IActionResult Add([FromForm] String name, [FromForm] IFormFile claPic, [FromForm] Int32 dataFileClass, [FromForm] Int32 sort)
        {
            if (name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
            }
            if (claPic == null)
            {
                return Prompt(new PromptModel { Message = GetResource("资料不能为空"), IsOk = false });
            }
            var model = DataFile.FindByName(name);
            if (model != null)
            {
                return Prompt(new PromptModel { Message = GetResource("标题已经存在"), IsOk = false });
            }
            model = new DataFile()
            {
                Name = name,
                DataFileClass = dataFileClass,
                Sort = sort,
                Show = true,
            };
            if (claPic != null)
            {
                var bytes = claPic.OpenReadStream().ReadBytes(claPic.Length);
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic.FileName)}";
                var filepath = $"DataFile/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic.SaveAs(saveFileName);
                model.Url = filepath.Replace("\\", "/");
            }
            model.Insert();
            var modelLocalizationSettings = LocalizationSettings.Current;
            if (modelLocalizationSettings.IsEnable)
            {
                var listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                var filea = Request.Form.Files;
                foreach (var modelLanguage in listLanguage)
                {
                    var lan = new DataFileLan()
                    {
                        DId = model.Id,
                        Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim(),
                        LId = modelLanguage.Id,
                    };
                    var file = filea.Where(e => e.Name == $"[{modelLanguage.Id}].claPic").FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"DataFileLan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        lan.Url = filepath.Replace("\\", "/");
                    }
                    lan.Insert();
                }
            }
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("编辑")]
        public IActionResult Update(Int32 Id)
        {
            dynamic viewModel = new ExpandoObject();
            var model = DataFile.FindById(Id);
            if (model == null) return Content(GetResource("资料不存在"));
            viewModel.listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            viewModel.Data = model;
            return View(viewModel);
        }

        [HttpPost]
        public IActionResult Update([FromForm]Int32 Id,[FromForm] String name, [FromForm] IFormFile claPic, [FromForm] Int32 dataFileClass, [FromForm] Int32 sort)
        {
            if (name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("标题不能为空"), IsOk = false });
            }
            var model = DataFile.FindByName(name);
            if (model != null && Id != model.Id)
            {
                return Prompt(new PromptModel { Message = GetResource("标题已经存在"), IsOk = false });
            }
            var item = DataFile.FindById(Id);
            if (item == null)
            {
                return Prompt(new PromptModel { Message = GetResource("资料不存在"), IsOk = false });
            }
            item.Name = name;
            item.DataFileClass = dataFileClass;
            item.Sort = sort;
            if (claPic != null)
            {
                var bytes = claPic.OpenReadStream().ReadBytes(claPic.Length);
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic.FileName)}";
                var filepath = $"DataFile/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic.SaveAs(saveFileName);
                item.Url = filepath.Replace("\\", "/");
            }
            item.Update();
            var modelLocalizationSettings = LocalizationSettings.Current;
            if (modelLocalizationSettings.IsEnable)
            {
                var listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                var filea = Request.Form.Files;
                foreach (var modelLanguage in listLanguage)
                {
                    var lan = DataFileLan.FindByDIdAndLId(item.Id, modelLanguage.Id);
                    if(lan == null)
                    {
                        lan = new DataFileLan()
                        {
                            DId = item.Id,
                            Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim(),
                            LId = modelLanguage.Id,
                        };
                        var file = filea.Where(e => e.Name == $"[{modelLanguage.Id}].claPic").FirstOrDefault();
                        if (file != null)
                        {
                            var bytes = file.OpenReadStream().ReadBytes(file.Length);
                            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                            var filepath = $"DataFileLan/{filename}";
                            var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                            filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                            saveFileName.EnsureDirectory();
                            file.SaveAs(saveFileName);
                            lan.Url = filepath.Replace("\\", "/");
                        }
                        lan.Insert();
                    }
                    else
                    {
                        lan.Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim();
                        var file = filea.Where(e => e.Name == $"[{modelLanguage.Id}].claPic").FirstOrDefault();
                        if (file != null)
                        {
                            var bytes = file.OpenReadStream().ReadBytes(file.Length);
                            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                            var filepath = $"DataFileLan/{filename}";
                            var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                            filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                            saveFileName.EnsureDirectory();
                            file.SaveAs(saveFileName);
                            lan.Url = filepath.Replace("\\", "/");
                        }
                        lan.Update();
                    }
                }
            }
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除")]
        public IActionResult Delete(String Ids)
        {
            DataFile.Delete(DataFile._.Id.In(Ids.Trim(',')));
            DataFileLan.Delete(DataFileLan._.DId.In(Ids.Trim(',')));
            return Json(new { success = true });
        }

        /// <summary>
        /// 修改显示状态
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateShow(Int32 Id)
        {
            var model = DataFile.FindById(Id);
            if (model == null) return Json(new { success = false, msg = GetResource("资料不存在") });
            model.Show = !model.Show;
            model.Update();
            return Json(new { success = true });
        }
    }
}
