<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电源网</title>
  <!-- 第三方css -->
  <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
  <link rel="stylesheet" href="../../modules/layui/css/layui.css">
  <!-- 第三方库js -->
  <script src="../../modules/jq.js"></script>
  <script src="../../modules/layui/layui.js"></script>
  <script src="../../modules/xm-select/xm-select.js"></script>
  <!-- 基础css -->
  <link rel="stylesheet" href="../../css/public.css">
  <link rel="stylesheet" href="../../css/header.css">
  <link rel="stylesheet" href="../../css/footer.css">
  <link rel="stylesheet" href="../../css/header.css">
  <link rel="stylesheet" href="../../css/media.css">
  <link rel="stylesheet" href="../../css/mySidebar.css">
  <!-- 本页私有css -->
  <link rel="stylesheet" href="../../css/pageCss/downloads.css">
  <link rel="stylesheet" href="../../css/pageCss/sample.css">
  <!-- 综合-script -->
  <script src="../../script/index.js" defer></script>
  <!-- 产品分类模块 -->
  <script src="../../script/productCategory.js" defer></script>
  <!-- 所有页面组成部分 -->
  <script src="../../components/common.js" defer></script>
  <script src="../../components/mySidebar.js" defer></script>
  <!-- 单页面组成部分 -->
  <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
  <!-- 引入头部组件 -->
  <div id="header"></div>

  <div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
      <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
      <div>></div>
      <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">应用支持</div>
    </div>
    <div class="dataDown">
      <!-- 侧边栏 -->
      <div class="mySidebar"></div>
      </video>

      <div class="dataDown-right">
        <div class="search">
          <div class="h3">成品检测报告</div>
        </div>
        <p class="tip">请输入产品序号进行查询,仅支持查询2023年10月以后所生产的产品检测报告,如查询无相关报告显示,请联系我司获取帮助。<a href="#">查看联系方式</a></p>
        <form class="layui-form">

          <div class="layui-form-item">
            <label class="layui-form-label">产品序号</label>
            <div class="layui-input-block">
              <input type="text" name="serial" lay-verify="required" placeholder="请输入" autocomplete="off"
                class="layui-input" value="1">
            </div>
          </div>

          <div class="layui-form-item">
            <label class="layui-form-label">验证码</label>
            <div class="layui-input-inline">
              <div class="layui-row">
                <div class="layui-col-xs7">
                  <div class="layui-input-wrap">
                    <input type="text" name="captcha" value="" lay-verify="required" placeholder="" lay-reqtext="请填写验证码"
                      autocomplete="off" class="layui-input" lay-affix="clear">
                  </div>
                </div>
                <div class="layui-col-xs5">
                  <div style="margin-left: 10px;">
                    <img src="https://www.oschina.net/action/user/captcha"
                      onclick="this.src='https://www.oschina.net/action/user/captcha?t='+ new Date().getTime();">
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="layui-col-xs4">&nbsp;</div>
          <div class="layui-col-xs4">
            <div class="layui-form-item">
              <button class="layui-btn layui-bg-blue" lay-submit lay-filter="demo1">确认</button>
            </div>
          </div>
        </form>

      </div>


    </div>

  </div>
  </div>

  <div class="bug"></div>

  <!-- 引入底部组件 -->
  <div id="footer"></div>
</body>
<script>
  const data = [
    { text: '应用支持', link: '#' },  // 标题
    { text: '焦点专题', link: '#' },
    { text: '资料下载', link: './downloads.html' },
    { text: '应用视频', link: './videos.html' },
    { text: '常见问题', link: './faq.html' },
    { text: '样品申请', link: './sample.html' },
    { text: '成品检测报告', link: './reports.html' }
  ];
  document.addEventListener('DOMContentLoaded', function () {
    layui.use(['form', 'laydate', 'util'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var laydate = layui.laydate;
      var util = layui.util;

      // 提交事件
      form.on('submit(demo1)', function (data) {
        var field = data.field; // 获取表单字段值
        // 显示填写结果
        console.log('field :>> ', field);

        // 此处可执行 Ajax 等操作
        // …

        return false; // 阻止默认 form 跳转
      });

      // 日期
      // laydate.render({
      //   elem: '#date'
      // });

    });
  });








</script>


</html>