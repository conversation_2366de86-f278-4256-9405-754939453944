﻿using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;
using System.Dynamic;

using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers;

/// <summary>
/// 多语言
/// </summary>
[DisplayName("多语言")]
[Description("用于系统多语言本地化管理")]
[AdminArea]
[DHMenu(50, ParentMenuName = "Settings", ParentMenuDisplayName = "设置", ParentMenuUrl = "~/{area}/Config", ParentMenuOrder = 95, ParentVisible = true, CurrentMenuUrl = "~/{area}/Culture", CurrentMenuName = "CultureList", CurrentIcon = "&#xe6a3;", LastUpdate = "20250527")]
public class CultureController : PekCubeAdminControllerX {
    /// <summary>多语言列表</summary>
    /// <returns></returns>
    [DisplayName("多语言列表")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult Index(string name, Int32 status = -1, int page = 1)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 10,
            OrderBy = "DisplayOrder",
            Desc = false,
            RetrieveTotalCount = true
        };

        name = name.SafeString().Trim();
        var list = Language.Searchss(name, status, pages);

        viewModel.list = list;
        viewModel.page = page;
        viewModel.name = name;
        viewModel.status = status;

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<String, String> { { "name", name } });
        return View(viewModel);
    }

    /// <summary>添加多语言</summary>
    /// <returns></returns>
    [DisplayName("添加多语言")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddCulture()
    {
        var DisplayOrder = Language.FindMax("DisplayOrder");
        ViewBag.DisplayOrder = DisplayOrder + 1;
        return View();
    }

    /// <summary>添加多语言</summary>
    /// <returns></returns>
    [DisplayName("添加多语言")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult AddCulture(String Name, String DisplayName, String EnglishName, String LanguageCulture, String UniqueSeoCode, String Flag, String Domain, Int32 Lcid, Int32 Status, Int32 DisplayOrder, String Remark, Int32 IsDefault)
    {
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        if (DisplayName.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("显示名称不能为空") });
        }
        if (EnglishName.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("英文名不能为空") });
        }
        if (LanguageCulture.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("语言缩写不能为空") });
        }
        if (UniqueSeoCode.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("Url缩写不能为空") });
        }

        Name = Name.SafeString().Trim();

        var Model = Language.FindByName(Name);
        if (Model != null)
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称已存在") });
        }
        Model = new Language
        {
            Name = Name,
            DisplayName = DisplayName.SafeString().Trim(),
            EnglishName = EnglishName,
            LanguageCulture = LanguageCulture,
            UniqueSeoCode = UniqueSeoCode,
            Flag = Flag,
            Domain = Domain,
            Lcid = Lcid,
            Status = Status == 1,
            DisplayOrder = DisplayOrder,
            Remark = Remark
        };
        Model.Insert();

        if (IsDefault == 1)
        {
            var modelDefault = Language.FindByDefault();
            if (modelDefault != null)
            {
                modelDefault.IsDefault = 0;
                modelDefault.Update();
            }

            Model.IsDefault = 1;
            Model.Update();
        }

        return Prompt(new PromptModel { Message = GetResource("保存成功"), IsOk = true, BackUrl = Url.Action("Index")! });
    }

    /// <summary>
    /// 修改多语言
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("修改多语言")]
    public IActionResult EditCulture(int Id)
    {
        var Model = Language.FindById(Id);
        return View(Model);
    }

    /// <summary>修改多语言</summary>
    /// <returns></returns>
    [DisplayName("修改多语言")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditCulture(Int32 Id, String Name, String DisplayName, String EnglishName, String LanguageCulture, String UniqueSeoCode, String Flag, String Domain, Int32 Lcid, Int32 Status, Int32 DisplayOrder, String Remark, Int32 IsDefault)
    {
        if (Name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称不能为空") });
        }
        if (DisplayName.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("显示名称不能为空") });
        }
        if (EnglishName.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("英文名不能为空") });
        }
        if (LanguageCulture.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("语言缩写不能为空") });
        }
        if (UniqueSeoCode.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("Url缩写不能为空") });
        }

        Name = Name.SafeString().Trim();
        var Model = Language.FindByName(Name);
        if (Model != null && Model.Id != Id)
        {
            return Prompt(new PromptModel { Message = GetResource("标题名称已存在") });
        }
        Model = Language.FindById(Id);
        if (Model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在") });
        }

        Model.Name = Name;
        Model.DisplayName = DisplayName.SafeString().Trim();
        Model.EnglishName = EnglishName;
        Model.LanguageCulture = LanguageCulture.SafeString().Trim();
        Model.UniqueSeoCode = UniqueSeoCode.SafeString().Trim();
        Model.Flag = Flag;
        Model.Domain = Domain;
        Model.Lcid = Lcid;
        Model.Status = Status == 1;
        Model.DisplayOrder = DisplayOrder;
        Model.Remark = Remark;
        Model.Update();

        if (IsDefault == 1)
        {
            var modelDefault = Language.FindByDefault();
            if (modelDefault != null)
            {
                modelDefault.IsDefault = 0;
                modelDefault.Update();
            }

            Model.IsDefault = 1;
            Model.Update();
        }

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true, BackUrl = Url.Action("Index")! });
    }

    /// <summary>
    /// 多语言数据删除
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("多语言数据删除")]
    public IActionResult Delete(string Ids)
    {
        var res = new DResult();
        Language.DelByIds(Ids);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
