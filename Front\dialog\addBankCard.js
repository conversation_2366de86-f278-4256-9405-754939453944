function addBankCard(id = "S02412069039") {
layui.layer.open({
    type: 1,
    // area: size,
    skin: 'dialog_addBankCard',
    resize:true,
    shadeClose: true,
    title:'添加新银行卡',
    btn: ['保存'],
    btn1: function (index, layero) {
    layer.close(index)
    console.log(index,layero);
    return false;
    },
      content: `
      <style>
        .dialog_addBankCard{
          padding: 10px 0px;
          border-radius: 18px;
        }
        .dialog_addBankCard>.layui-layer-title{
          font-size: 1vw;
          letter-spacing: 1px;
          color: black;
        }
        /* bomBox 自定义按钮 */
        .dialog_addBankCard>.layui-layer-btn {
          width: 100%;
          padding: 0px;
          margin: 0px;
          box-sizing: border-box;
          /* border: 2px solid ; */
          margin-bottom: 1vw;
        }

        .dialog_addBankCard>.layui-layer-btn>a {
          width: 60%;
          margin-right: 20%;
          max-height: 35px;
          height: 2.5vw;
          min-height: 32px;
          text-align: center;
          background-color: var(--blue-deep);
        }


        .addBankCardBox {
          min-width: 400px;
          padding: 1vw 1vw .5vw 1vw;
        }
        .addBankCardBox>div {
          margin-bottom: .5vw;
          font-size: .75vw;
          letter-spacing: 1px;
        }

        .addCardText {
          font-size: .8vw;
          margin-right: 3px;
        }
        .addCardText2 {
          font-size: .7vw;
          color: var(--text-color1);
        }
        .bandCard_inputBox{
          width: 100%;
          display: flex;
        }
        /* input */
        .addBankCard_input{
          height: 40px;
          color: var(--text-color2);
          border-radius: 7px;
        }
        .color2{
          color: var(--text-color2);
          transition: .7s;
        }
      </style>
      <div class="addBankCardBox">
        <div class="flex" style="justify-content: left;background: rgba(44,121,232,0.2);padding: 10px 10px;">
          <div class="addCardText">添加新银行卡</div>
          <div>
            <img src="../../images/icons/mastercam-baidi.png" alt="">
          </div>
          <div> <img src="../../images/icons/VISA.png" alt=""> </div>
        </div>
        <div class="addCardText2">卡号</div>
        <div class="layui-form-item">
          <div class="layui-input-group" style="width: 100%;position: relative;">
            <input type="text" placeholder="带任意前置和后置内容" class="layui-input addBankCard_input" maxlength="43">
            <i class="iconfont icon-creditcard hoverBlue color2" style="position: absolute;right: 5%;top:8px;font-size: 25px;"></i>    
          </div>
        </div>
        <div class="bandCard_inputBox">
          <div style="flex: 1;">
            <div class="addCardText2">日期</div>
            <div class="layui-inline">
              <div class="layui-input-inline">
                <input type="text" class="layui-input addBankCard_input" id="bankCard_date" placeholder="yyyy-MM" style="margin-top: 5px;">
              </div>
            </div>
          </div>
          <div style="flex: 1;">
            <div class="addCardText2">安全码</div>
            <div class="layui-inline">
              <div class="layui-input-inline layui-input-wrap">
                <input type="text" placeholder="3位" class="layui-input addBankCard_input" maxlength="3" style="margin-top: 5px;margin-left: auto;">
              </div>
            </div>
          </div>
        </div>
        <script>
          // 英文版
          layui.laydate.render({
            elem: '#bankCard_date',
            lang: 'en'
          });
        </script>
      </div>
      `
      })
}

// function bomConfirm(closeIndex) {
// // console.log(closeIndex);
// layui.layer.closeAll()
// }