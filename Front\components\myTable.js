document.addEventListener('DOMContentLoaded', function() {
    // 加载表格和分页器组件
    const tableElement = document.getElementById('myTable');
    if (tableElement) {
        fetch('../../components/myTable.html')
            .then(response => {
                // console.log('表格响应:', response);
                return response.text();
            })
            .then(data => {
                // console.log('表格数据已接收:', data.slice(0,200));
                tableElement.innerHTML = data;
                // 在表格加载完成后初始化分页器
                layui.use(['laypage'], function() {
                    var laypage = layui.laypage;
                    // 完整显示
                    laypage.render({
                        elem: 'demo-laypage-all', // 元素 id
                        count: 100, // 数据总数
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 功能布局
                        jump: function (obj) {
                            // console.log(obj);
                        }
                    });
                });
            })
            .catch(error => {
                console.error('加载表格出错:', error);
            });
    } else {
        console.warn('未找到ID为"myTable"的表格元素');
    }
});