/* 头部样式  -公共 */
.header {
  width: 90%;
  padding: 1vw 5% 1.5vw 5%;
  display: flex;
  justify-content: center;
  /* border: 1px solid red; */
  margin: auto;
  border-bottom: 1px solid var(--line);
}

.headerItem {
  /* width: 200px; */
  padding: 5px 10px;
  height: 1.8vw;
  margin: 5px;
  font-size: 1em;
  /* border: 1px solid red; */
  display: flex;
  justify-content: space-between;
  place-items: center;
  position: relative;
}

.headerItem>.hoverText:hover {
  transition: all 0.3s;
  color: black;
  cursor: pointer;
}

.header_keyBox {
  position: absolute;
  top: 115%;
  left: 2%;
  padding-bottom: 10px;
  font-size: 12.5px;
  color: var(--text-color);
}

.header_key:hover {
  color: var(--blue-deep);
}

/* 关键字 input 用于判定是否选中 */
.header_keyCheck[type="radio"] {
  display: none;
}

/* 关键字 */
.header_key {
  padding: 0px 10px;
  font-size: var(--size0);
  cursor: pointer;
  transition: all 0.3s;
}

/* 当对应的单选按钮被选中时，改变菜单按钮的背景颜色 */
.header_keyCheck[type="radio"]:checked+.header_key {
  color: var(--blue);
}

.headLogo {
  width: 140px;
  height: 80px;
  object-fit: contain;
  transform: scale(0.8);
}

.blur {
  width: 140px;
  background-color: rgba(255, 255, 255, 0.3);
  /* 半透明背景 */
  backdrop-filter: blur(10px);
  /* 毛玻璃模糊效果 */
  -webkit-backdrop-filter: blur(10px);
  /* Safari 兼容 */
  margin-right: 20px;

}

.searchBox {
  width: 100%;
  height: 2vw;
  min-height: 30px;
  border: 2px solid var(--blue-deep);
  box-sizing: border-box;
  border-radius: 50px;
  /* overflow: hidden; */
  background-color: var(--bd);
  display: flex;
  place-items: center;
}

/* 取消掉产品分类select的默认样式 */
.headerForm>.layui-form-select>.layui-select-title>.layui-input {
  all: unset;
  user-select: none;
  text-align: center;
  width: 90%;
  font-size: 0.9em;
}

.headerForm>.layui-form-select {
  margin-top: -3px;
  width: 5vw !important;
  min-width: 100px !important;
  user-select: none;
  /* border: 1px solid red;*/
}

.searchInput {
  border: none !important;
  padding: 0px 10px;
  font-size: 14px;
  width: 100%;
  outline: none;
  box-shadow: none;
  color: var(--text-color);
  background-color: transparent;
}

.searchInput::placeholder {
  font-size: 13px !important;
  letter-spacing: 1px;
}

.my-search {
  margin-right: 25px;
  font-size: 20px;
  color: var(--myBlue);
  cursor: pointer;
}

.searchButton {
  width: 25%;
  min-width: 80px;
  background-color: var(--myBlue);
  height: 100%;
  border-radius: 0 15px 15px 0px;
  color: white;
  border: none;
}

.header1 {
  width: 100%;
  height: 100%;
  background-color: var(--myBlue) !important;

}

.header2 {
  width: 1340px;
  margin: 0 auto;
  display: flex;
  place-items: center;
  background-color: var(--myBlue);
  color: white;
  position: relative;
  overflow: visible;
  font-weight: 400;
  /* border: 1px solid #000; */
}

.header2>.headerForm>.layui-form-select>.layui-select-title>.layui-input {
  color: white;
}

.header2>div {
  /* padding: 0.3vw; */
  height: 76px;
  margin-left: 12px;
  font-size: 17px;
  display: flex;
  align-items: center;
  border-bottom: 4px solid var(--myBlue);

}

.header2>div:hover {
  border-bottom: 4px solid var(--white);
}

.supportBox {
  position: relative;
  cursor: pointer;
}

.supportBox:hover .support {
  display: block;
  border-bottom: 4px solid var(--white);
}

.support {
  display: none;
  /* display: block; */
  width: 120px;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 4px;
  color: #000;
  background-color: var(--white);
  font-size: 16px;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.support p {
  margin: 8px 0;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
  text-align: center;
}

.support p:hover {
  background-color: #0E6AF21A;
  color: var(--blue-deep);
}



.supportBox:hover .support2 {
  display: flex;
  align-content: center;
  border-bottom: 4px solid var(--white);
}

.support2 {
  width: 1200px;
  display: none;
  position: absolute;
  top: 100%;
  left: -538px;
  transform: translateX(-50%);
  margin-top: 4px;
  color: #000;
  background-color: var(--white);
  font-size: 16px;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.support2 ul {
  margin: 0 30px;
}

.support2 ul li {
  padding: 5px 10px;
}

.support2 ul li:first-child {
  font-weight: bold;
  border-bottom: 1px solid #c0c0c0;
  
}


.noAct {
  border-bottom: 4px solid transparent !important;
}



/* 关键字 */
.header2_key {
  padding: 0px 10px;
  height: 40px !important;
  /* line-height: 40px; */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  max-width: 88px;
}

.lngText {
  cursor: pointer;
  padding: 0 30px;
  /* border: 1px solid #000; */
}

.cut {
  min-width: 68px;
}

.showLngBox {
  position: absolute;
  top: 100%;
  /* right: -10%; */
  min-width: 200px;
  min-height: 160px;
  width: 14vw !important;
  height: auto !important;
  max-height: 260px;
  background-color: white;
  z-index: 99999;
  border-radius: 15px;
  border: 1px solid #e2e3e9;
  box-shadow: 4px 2px 8px 0px rgba(0, 0, 0, 0.15),
    -4px -2px 8px 0px rgba(0, 0, 0, 0.15);
  padding: 1.5vw 1vw;
  color: var(--text-color);
}

/* .showLngBox>div {
    padding: 1vw 1vw;
} */

.showLngBox>div:nth-child(2n-1) {
  font-size: 1vw;
  text-indent: 20px;
}

.showLngBox>div:nth-child(2n) {
  padding: 0.35vw 0vw;
  margin-left: 20px;
}

.productCategory {
  position: relative;
  user-select: none;
  cursor: pointer;
  border-bottom: 4px solid var(--myBlue);
}


.productCategory:hover {
  border-bottom: 4px solid var(--white);
  z-index: 999;
}

.productCategory:hover .productCategoryBox {
  display: block;
}

/* 产品分类 开始 */
.productCategoryBox {
  margin-top: 4px;
  position: absolute;
  top: 100%;
  left: 0%;
  width: 16vw;
  height: auto;
  background-color: white;
  z-index: 888;
  border-radius: 25px;
  display: none;
  /* display: block; */

}

.productCategory_onePage {

  position: absolute;
  width: 100%;
  height: 40vh;
  background-color: white;
  overflow-y: scroll;
  border-radius: 10px;
  z-index: 2;
}

.productCategory_onePage>div {
  padding: 15px 0px;
  display: flex;
  justify-content: space-between;
  color: var(--text-color);
  cursor: pointer;
  white-space: nowrap;
  text-align: left;
  font-size: 15px;
  text-indent: 1.7cqw;

}




.productCategory_hoverPages {
  position: relative;
  left: 100%;
  top: 0%;
  width: 35vw;
  height: 40vh;
  background-color: white;
  display: flex;
  z-index: 2;
  border: 1px solid var(--line);
  border-radius: 0px 10px 10px 0px;
  overflow: auto
}

.productCategory_onePage>div:hover {
  color: var(--blue-deep);
  background-color: #0E6AF21A;
}

.productCategory_hoverPages>div>div>h5 {
  margin-bottom: 10px;
}

.productCategory_hoverPages>div>div>h5:hover {

  color: var(--blue-deep);
  /* color: red; */
}

.tag {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag span {
  width: 23%;
  color: #666;
  font-size: 15px;

}

.productCategory_onePage:hover~.hoverPages {
  display: flex !important;
}

.productCategory_hoverPages:hover {
  display: flex;
}

.productCategory_hoverPages>div>div {
  width: 35vw;
  padding: 16px 0px;
  color: var(--text-color);
  cursor: pointer;
  white-space: nowrap;
  text-align: left;
  text-indent: 1.7cqw;



}

.twoPage {
  flex: 3;
  overflow-y: scroll;
  width: 35W;
}

.twoPage>div[data-select="true"] {
  color: var(--blue-deep);
}

.productCategory_onePage>div[data-select="true"] {
  color: var(--blue-deep);
}

