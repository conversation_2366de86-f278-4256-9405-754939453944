<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电源网</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/media.css">
    <link rel="stylesheet" href="../../css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/productDetail.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js" ></script>
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
    <!-- 产品分类模块 -->
    <script src="../../script/productCategory.js"></script>

     <!-- 所有页面组成部分 -->
     <script src="../../components/common.js" defer></script>
     <!-- 单页面组成部分 -->
     <script src="../../components/mySidebar.js" defer></script>
     <script src="./components/page.js" defer></script>
     
</head>

<body>
    <!-- 引入头部组件 -->
    <div id="header"></div>

    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./productCenter.html">产品分类</div>
        </div>
        <div class="contentBox">
            <!-- 产品分类 -->
            <div class="mySidebar"></div>
            <div class="content">
                <div class="productBox">
                    <div class="productTitle" onclick="toRouter(this)"
                        data-link="../productCategory/productCategory.html">ACDC电源模组
                        <span class="count"></span>
                    </div>
                    <div class="productList">
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                    </div>
                </div>

                <div class="productBox">
                    <div class="productTitle" onclick="toRouter(this)"
                        data-link="../productCategory/productCategory.html">ACDC电源模组
                        <span class="count"></span>
                    </div>
                    <div class="productList">
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                    </div>
                </div>


                <div class="productBox">
                    <div class="productTitle" onclick="toRouter(this)"
                        data-link="../productCategory/productCategory.html">ACDC电源模组
                        <span class="count"></span>
                    </div>
                    <div class="productList">
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                    </div>
                </div>

                <div class="productBox">
                    <div class="productTitle" onclick="toRouter(this)"
                        data-link="../productCategory/productCategory.html">ACDC电源模组
                        <span class="count"></span>
                    </div>
                    <div class="productList">
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                        <div class="introduceItem">AC/DC电源#功率2W <span class="count"></span> </div>
                    </div>
                </div>

            </div>
        </div>

    </div>
    <div class="bug"></div>

    <!-- 引入底部组件 -->
    <div id="footer"></div>

    <!-- 加载组件的脚本 -->
    <script>
       const data =[
        {
            text:'产品分类中心',
            link:'./productCenter.html',
        },
        {
            text:'AC/DC(2-5W)隔离模块',
            link:'./productCategory.html',
        },
        {
            text:'AC/DC(20-60W)隔离模块',
            link:'',
        },
        {
            text:'机壳开关电源模块',
            link:'',
        },
        {
            text:'DC/DC定电压隔离模块',
            link:'',
        },
        {
            text:'DC/DC(3-15W)隔离模块',
            link:'',
        },
        {
            text:'DC/DC(20-50W)隔离模块',
            link:'',
        },
        {
            text:'DC/DC非隔离模块',
            link:'',
        },
       ]
    </script>
</body>

</html>