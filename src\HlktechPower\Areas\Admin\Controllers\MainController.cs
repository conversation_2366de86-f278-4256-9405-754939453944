﻿using Microsoft.AspNetCore.Mvc;

using Pek.DsMallUI;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;

using System.ComponentModel;

using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers;

/// <summary>欢迎界面</summary>
[DisplayName("欢迎界面")]
[Description("后台登录之后的首页调用的默认区域")]
[AdminArea]
[DHMenu(99, ParentMenuName = "Home", ParentMenuDisplayName = "控制台", ParentMenuUrl = "~/{area}/Home/DashBoard", CurrentMenuUrl = "~/{area}/Main", CurrentMenuName = "Main", CurrentIcon = "&#xe70b;", LastUpdate = "20250526")]
public class MainController : PekCubeAdminControllerX {
    /// <summary>
    /// 欢迎界面
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("欢迎界面")]
    public IActionResult Index()
    {
        return View();
    }
}
