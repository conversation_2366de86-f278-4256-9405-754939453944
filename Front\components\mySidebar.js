// 侧边栏组件
const DynamicSidebar = {
  init: function (config) {
    const {
      data,
      containerSelector = ".mySidebar",
      activeItem = null,
    } = config;

    const container = document.querySelector(containerSelector);
    if (!container) {
      console.warn(`找不到侧边栏容器: ${containerSelector}`);
      return false;
    }

    if (!Array.isArray(data) || data.length === 0) {
      console.error("侧边栏数据必须是非空数组");
      return false;
    }

    this.generateSidebar(container, data, activeItem);
    
    // 根据当前URL路径自动高亮对应菜单项
    this.highlightActiveItemByUrl();
    
    return true;
  },

  generateSidebar: function (container, data, activeItem) {
    // 处理标题
    const titleItem = data[0];
    const titleText =
      typeof titleItem === "object" ? titleItem.text : titleItem;
    const titleLink = typeof titleItem === "object" ? titleItem.link : "#";
    const titleHTML =
      titleLink !== "#"
        ? `<h2 class="title" onclick="toRouter(this)" data-link="${titleLink}">${titleText}</h2>`
        : `<h2 class="title">${titleText}</h2>`;

    // 处理菜单项
    const menuItems = data
      .slice(1)
      .map((item) => {
        const itemText = typeof item === "object" ? item.text : item;
        const itemLink = typeof item === "object" ? item.link : "#";
        const isActive = activeItem ? itemText === activeItem : false;
        const activeClass = isActive ? " textSelect" : "";

        return `<li class="${activeClass.trim()}"
                        onclick="DynamicSidebar.handleItemClick(this)"
                        data-link="${itemLink}"
                        data-menu-item="${itemText}">
                        ${itemText}
                    </li>`;
      })
      .join("");

    const sidebarHTML = `
            <ul>
                ${titleHTML}
                ${menuItems}
            </ul>
        `;

    container.innerHTML = sidebarHTML;
  },

  /**
   * 处理菜单项点击事件
   * @param {Element} element - 被点击的元素
   */
  handleItemClick: function (element) {
    // 保存激活状态到 sessionStorage，以便新页面恢复
    const menuItem = element.getAttribute("data-menu-item");
    if (menuItem) {
      sessionStorage.setItem("activeSidebarItem", menuItem);
    }
    // 更新选中状态（如果不是标题）
    if (element.tagName.toLowerCase() === "li") {
      this.updateActiveState(element);
    }

    // 执行跳转
    toRouter(element);
  },

  /**
   * 更新激活状态
   * @param {Element} activeElement - 当前激活的元素
   */
  updateActiveState: function (activeElement) {
    // 移除同级所有元素的激活状态
    const allItems = activeElement.parentElement.querySelectorAll("li");
    allItems.forEach((item) => item.classList.remove("textSelect"));

    // 添加当前元素的激活状态
    activeElement.classList.add("textSelect");
  },

  /**
   * 设置激活项
   * @param {string} menuItem - 要激活的菜单项名称
   * @param {string} containerSelector - 容器选择器
   */
  setActiveItem: function (menuItem, containerSelector = ".mySidebar") {
    const container = document.querySelector(containerSelector);
    if (!container) return;

    const targetItem = container.querySelector(
      `[data-menu-item="${menuItem}"]`
    );
    if (targetItem && targetItem.tagName.toLowerCase() === "li") {
      this.updateActiveState(targetItem);
    }
  },
  
  /**
   * 根据当前URL路径高亮对应的菜单项
   */
  highlightActiveItemByUrl: function() {
    // 确保DOM已加载完成
    setTimeout(() => {
      const currentPath = window.location.pathname;
      const currentUrl = window.location.href;
      
      console.log('当前URL:', currentUrl);
      console.log('当前路径:', currentPath);
      
      // 提取当前页面的文件名
      const currentPageName = currentPath.split('/').pop();
      console.log('当前页面文件名:', currentPageName);
      
      // 查找所有菜单项
      const menuItems = document.querySelectorAll('.mySidebar li[data-link]');
      console.log('找到菜单项数量:', menuItems.length);
      
      let foundMatch = false;
      
      // 遍历所有菜单项，检查链接是否匹配当前URL
      menuItems.forEach(item => {
        const itemLink = item.getAttribute('data-link');
        const itemText = item.getAttribute('data-menu-item');
        console.log(`检查菜单项: "${itemText}" 链接: "${itemLink}"`);
        
        if (!itemLink || itemLink === '#') {
          return;
        }
        
        // 提取链接中的文件名
        let linkFileName = '';
        
        // 处理相对路径 (如 ./downloads.html)
        if (itemLink.startsWith('./')) {
          linkFileName = itemLink.substring(2);
        } 
        // 处理绝对路径 (如 /pages/appSupport/downloads.html)
        else if (itemLink.includes('/')) {
          linkFileName = itemLink.split('/').pop();
        }
        // 直接文件名 (如 downloads.html)
        else {
          linkFileName = itemLink;
        }
        
        console.log(`菜单项文件名: "${linkFileName}"`);
        
        // 匹配逻辑
        let isMatch = false;
        
        // 1. 完全URL匹配
        if (currentUrl === itemLink) {
          console.log('完全URL匹配');
          isMatch = true;
        }
        // 2. 完全路径匹配
        else if (currentPath === itemLink) {
          console.log('完全路径匹配');
          isMatch = true;
        }
        // 3. 文件名匹配 (最重要的匹配方式，适用于相对路径)
        else if (currentPageName === linkFileName) {
          console.log('文件名匹配');
          isMatch = true;
        }
        // 4. 路径结尾匹配
        else if (currentPath.endsWith(itemLink)) {
          console.log('路径结尾匹配');
          isMatch = true;
        }
        
        if (isMatch) {
          console.log(`找到匹配项: "${itemText}" 链接: "${itemLink}"`);
          
          // 高亮匹配的菜单项
          this.updateActiveState(item);
          
          // 保存激活状态到sessionStorage
          if (itemText) {
            sessionStorage.setItem('activeSidebarItem', itemText);
          }
          
          foundMatch = true;
        }
      });
      
      // 如果没有找到匹配项，尝试从sessionStorage恢复
      if (!foundMatch) {
        console.log('未找到匹配项，尝试从sessionStorage恢复');
        const savedActiveItem = sessionStorage.getItem('activeSidebarItem');
        if (savedActiveItem) {
          console.log('从sessionStorage恢复:', savedActiveItem);
          this.setActiveItem(savedActiveItem);
        }
      }
    }, 300); // 延迟执行，确保DOM已完全加载
  }
};

// 页面加载时初始化
document.addEventListener("DOMContentLoaded", () => {
  // 使用页面中的data数组
  if (typeof data !== "undefined" && Array.isArray(data)) {
    const success = DynamicSidebar.init({
      data: data,
      containerSelector: ".mySidebar",
    });

    if (success) {
      // 确保在页面完全加载后执行高亮
      window.addEventListener('load', () => {
        DynamicSidebar.highlightActiveItemByUrl();
      });
    }
  } else {
    console.error("未找到data数组或data不是数组类型");
  }
});

// 添加一个公共方法，允许在需要时手动触发高亮
DynamicSidebar.refreshActiveState = function() {
  this.highlightActiveItemByUrl();
};


