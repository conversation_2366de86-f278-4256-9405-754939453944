﻿using DH.Core.Domain.Localization;
using DH.Entity;
using HlktechPower.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Log;
using NewLife.Serialization;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Pek;
using Pek.DsMallUI;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;
using System.ComponentModel;
using System.Dynamic;
using System.Reflection.Emit;
using XCode;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers
{
    /// <summary>
    /// 产品分类
    /// </summary>
    [DisplayName("产品分类")]
    [Description("用于产品分类的管理")]
    [AdminArea]
    [DHMenu(100, ParentMenuName = "Product", ParentMenuDisplayName = "产品", ParentMenuUrl = "~/{area}/Product", ParentMenuOrder = 90, ParentVisible = true, CurrentMenuUrl = "~/{area}/ProductClass", CurrentMenuName = "ProductClass", CurrentIcon = "&#xe652;", LastUpdate = "20250527")]
    public class ProductClassController : PekCubeAdminControllerX
    {
        [EntityAuthorize(PermissionFlags.Detail)]
        [DisplayName("列表")]
        public IActionResult Index()
        {
            dynamic viewModel = new ExpandoObject();
            var listProductClass = ProductClass.FindAllByParentId(0).OrderBy(e=>e.Sort).Select(e=>new ProductClass
            {
                Id = e.Id,
                ParentId = e.ParentId,
                Sort = e.Sort,
                Name = ProductClassLan.FindByProductClass(e,WorkingLanguage.Id).Name,
                Enable = e.Enable,
                Level = e.Level,
            });
            viewModel.listProductClass = listProductClass;
            return View(viewModel);
        }

        [EntityAuthorize(PermissionFlags.Insert)]
        [DisplayName("添加")]
        public IActionResult Add(Int64 pId)
        {
            dynamic viewModel = new ExpandoObject();
            viewModel.listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            viewModel.pId = pId; //父级ID 顶级为0
            return View(viewModel);
        }

        [HttpPost]
        public IActionResult Add([FromForm]Int64 pId, [FromForm]String name,[FromForm]Int32 sort, [FromForm] IFormFile claPic, [FromForm]String description, [FromForm] String seoName, [FromForm] String seoKey, [FromForm] String seoDescription, [FromForm] String power, [FromForm] String inputVoltage, [FromForm] String outputVoltage, [FromForm] String outputCurrent, [FromForm] String isolationVoltage, [FromForm] String packagingForm, [FromForm] String packagingSize, [FromForm] IFormFile claPic2, [FromForm] IFormFile claPic3, [FromForm] IFormFile claPic4, [FromForm]String peculiarity, [FromForm]String inputVoltageVDC)
        {
            if (name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类名称不能为空"), IsOk = false });
            }
            var modelProductClass = ProductClass.FindByName(name);
            if(modelProductClass != null)
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类名称已经存在"), IsOk = false });
            }
            modelProductClass = new ProductClass()
            {
                ParentId = pId,
                Name = name,
                Description = description,
                SeoName = seoName,
                SeoKey = seoKey,
                SeoDescription = seoDescription,
                Sort = sort,
                Enable = true,
                Power = power,
                InputVoltageVAC = inputVoltage,
                InputVoltageVDC = inputVoltageVDC,
                OutputVoltage = outputVoltage,
                OutputCurrent = outputCurrent,
                IsolationVoltage = isolationVoltage,
                PackagingForm = packagingForm,
                PackagingSize = packagingSize,
                Peculiarity = peculiarity,
            };
            modelProductClass.Insert();
            if (pId == 0)
            {
                modelProductClass.Level = 0;
                modelProductClass.Ancestral = "," + modelProductClass.Id + ",";
            }
            else
            {
                var item = ProductClass.FindById(pId);
                if (item != null)
                {
                    modelProductClass.Level = item.Level + 1;
                    modelProductClass.Ancestral = item.Ancestral + modelProductClass.Id + ",";
                }
            }
            if (claPic != null)
            {
                var bytes = claPic.OpenReadStream().ReadBytes(claPic.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic.FileName)}";
                var filepath = $"ProductClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic.SaveAs(saveFileName);
                modelProductClass.Image = filepath.Replace("\\", "/");
            }
            if (claPic2 != null)
            {
                var bytes = claPic2.OpenReadStream().ReadBytes(claPic2.Length);
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic2.FileName)}";
                var filepath = $"ProductClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic2.SaveAs(saveFileName);
                modelProductClass.Information = filepath.Replace("\\", "/");
            }
            if (claPic3 != null)
            {
                var bytes = claPic3.OpenReadStream().ReadBytes(claPic3.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic3.FileName)}";
                var filepath = $"ProductClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic3.SaveAs(saveFileName);
                modelProductClass.Attestation = filepath.Replace("\\", "/");
            }
            if (claPic4 != null)
            {
                var bytes = claPic4.OpenReadStream().ReadBytes(claPic4.Length);
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic4.FileName)}";
                var filepath = $"ProductClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic4.SaveAs(saveFileName);
                modelProductClass.TechnicalManual = filepath.Replace("\\", "/");
            }
            modelProductClass.Update();
            var modelLocalizationSettings = LocalizationSettings.Current;
            if (modelLocalizationSettings.IsEnable)
            {
                var listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                var filea = Request.Form.Files;
                foreach (var modelLanguage in listLanguage)
                {
                    var modelProductClassLan = new ProductClassLan()
                    {
                        PId = modelProductClass.Id,
                        Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim(),
                        Image = (GetRequest($"[{modelLanguage.Id}].image")).SafeString().Trim(),
                        Description = (GetRequest($"[{modelLanguage.Id}].description")).SafeString().Trim(),
                        SeoName = (GetRequest($"[{modelLanguage.Id}].seoName")).SafeString().Trim(),
                        SeoKey = (GetRequest($"[{modelLanguage.Id}].seoKey")).SafeString().Trim(),
                        SeoDescription = (GetRequest($"[{modelLanguage.Id}].seoDescription")).SafeString().Trim(),
                        Peculiarity = (GetRequest($"[{modelLanguage.Id}].peculiarity")).SafeString().Trim(),
                        LId = modelLanguage.Id,
                    };
                    modelProductClassLan.Insert();
                    var file = filea.Where(e => e.Name == $"[{modelLanguage.Id}].claPic").FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"ProductClassLan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        modelProductClassLan.Image = filepath.Replace("\\", "/");
                        modelProductClassLan.Update();
                    }
                }
            }
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        /// <summary>
        /// 获取子分类列表
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public IActionResult GetChildlist(Int64 Id)
        {
            var listProductClass = ProductClass.FindAllByParentId(Id).OrderBy(e => e.Sort).Select(e => new 
            {
                Id = e.Id.SafeString(),
                ParentId = e.ParentId.SafeString(),
                Sort = e.Sort,
                Name = ProductClassLan.FindByProductClass(e, WorkingLanguage.Id).Name,
                Enable = e.Enable,
                Level = e.Level,
            });
            return Json(new { data = listProductClass });
        }

        [EntityAuthorize(PermissionFlags.Update)]
        [DisplayName("编辑")]
        public IActionResult Update(Int64 Id)
        {
            var modelProductClass = ProductClass.FindById(Id);
            if (modelProductClass == null)
            {
                return Content(GetResource("产品分类不存在"));
            }
            ViewBag.listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
            return View(modelProductClass);
        }

        [HttpPost]
        public IActionResult Update([FromForm]Int64 Id, [FromForm] String name, [FromForm] Int32 sort, [FromForm] IFormFile claPic, [FromForm] String description, [FromForm] String seoName, [FromForm] String seoKey, [FromForm] String seoDescription, [FromForm] String power, [FromForm] String inputVoltage, [FromForm] String outputVoltage, [FromForm] String outputCurrent, [FromForm] String isolationVoltage, [FromForm] String packagingForm, [FromForm] String packagingSize, [FromForm] IFormFile claPic2, [FromForm] IFormFile claPic3, [FromForm] IFormFile claPic4, [FromForm] String peculiarity, [FromForm] String inputVoltageVDC)
        {
            if (name.IsNullOrWhiteSpace())
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类名称不能为空"), IsOk = false });
            }
            var modelProductClass = ProductClass.FindByName(name);
            if (modelProductClass != null && modelProductClass.Id != Id)
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类名称已经存在"), IsOk = false });
            }
            var itemProductClass = ProductClass.FindById(Id);
            if (itemProductClass == null)
            {
                return Prompt(new PromptModel { Message = GetResource("产品分类不存在"), IsOk = false });
            }
            itemProductClass.Name = name;
            itemProductClass.Sort = sort;
            itemProductClass.Description = description;
            itemProductClass.SeoName = seoName;
            itemProductClass.SeoKey = seoKey;
            itemProductClass.SeoDescription = seoDescription;
            itemProductClass.Power = power;
            itemProductClass.InputVoltageVAC = inputVoltage;
            itemProductClass.InputVoltageVDC = inputVoltageVDC;
            itemProductClass.OutputVoltage = outputVoltage;
            itemProductClass.OutputCurrent = outputCurrent;
            itemProductClass.IsolationVoltage = isolationVoltage;
            itemProductClass.PackagingForm = packagingForm;
            itemProductClass.PackagingSize = packagingSize;
            itemProductClass.Peculiarity = peculiarity;
            if (claPic != null)
            {
                var bytes = claPic.OpenReadStream().ReadBytes(claPic.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic.FileName)}";
                var filepath = $"ProductClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic.SaveAs(saveFileName);
                itemProductClass.Image = filepath.Replace("\\", "/");
            }
            if (claPic2 != null)
            {
                var bytes = claPic2.OpenReadStream().ReadBytes(claPic2.Length);
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic2.FileName)}";
                var filepath = $"ProductClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic2.SaveAs(saveFileName);
                itemProductClass.Information = filepath.Replace("\\", "/");
            }
            if (claPic3 != null)
            {
                var bytes = claPic3.OpenReadStream().ReadBytes(claPic3.Length);
                if (!bytes.IsImageFile())
                {
                    return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                }
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic3.FileName)}";
                var filepath = $"ProductClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic3.SaveAs(saveFileName);
                itemProductClass.Attestation = filepath.Replace("\\", "/");
            }
            if (claPic4 != null)
            {
                var bytes = claPic4.OpenReadStream().ReadBytes(claPic4.Length);
                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(claPic4.FileName)}";
                var filepath = $"ProductClass/{filename}";
                var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                saveFileName.EnsureDirectory();
                claPic4.SaveAs(saveFileName);
                itemProductClass.TechnicalManual = filepath.Replace("\\", "/");
            }
            itemProductClass.Update();
            var modelLocalizationSettings = LocalizationSettings.Current;
            if (modelLocalizationSettings.IsEnable)
            {
                var listLanguage = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                var filea = Request.Form.Files;
                foreach (var modelLanguage in listLanguage)
                {
                    var modelProductClassLan = ProductClassLan.FindByPIdAndLId(itemProductClass.Id, modelLanguage.Id);
                    if(modelProductClassLan == null)
                    {
                        modelProductClassLan = new ProductClassLan()
                        {
                            PId = itemProductClass.Id,
                            Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim(),
                            Description = (GetRequest($"[{modelLanguage.Id}].description")).SafeString().Trim(),
                            SeoName = (GetRequest($"[{modelLanguage.Id}].seoName")).SafeString().Trim(),
                            SeoKey = (GetRequest($"[{modelLanguage.Id}].seoKey")).SafeString().Trim(),
                            SeoDescription = (GetRequest($"[{modelLanguage.Id}].seoDescription")).SafeString().Trim(),
                            Peculiarity = (GetRequest($"[{modelLanguage.Id}].peculiarity")).SafeString().Trim(),
                            LId = modelLanguage.Id,
                        };
                        modelProductClassLan.Insert();
                    }
                    else
                    {
                        modelProductClassLan.Name = (GetRequest($"[{modelLanguage.Id}].name")).SafeString().Trim();
                        modelProductClassLan.Description = (GetRequest($"[{modelLanguage.Id}].description")).SafeString().Trim();
                        modelProductClassLan.SeoName = (GetRequest($"[{modelLanguage.Id}].seoName")).SafeString().Trim();
                        modelProductClassLan.SeoKey = (GetRequest($"[{modelLanguage.Id}].seoKey")).SafeString().Trim();
                        modelProductClassLan.SeoDescription = (GetRequest($"[{modelLanguage.Id}].seoDescription")).SafeString().Trim();
                        modelProductClassLan.Peculiarity = (GetRequest($"[{modelLanguage.Id}].peculiarity")).SafeString().Trim();
                        modelProductClassLan.Update();
                    }
                    var file = filea.Where(e => e.Name == $"[{modelLanguage.Id}].claPic").FirstOrDefault();
                    if (file != null)
                    {
                        var bytes = file.OpenReadStream().ReadBytes(file.Length);
                        if (!bytes.IsImageFile())
                        {
                            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
                        }
                        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
                        var filepath = $"ProductClassLan/{filename}";
                        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                        filepath = $"{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                        saveFileName.EnsureDirectory();
                        file.SaveAs(saveFileName);
                        modelProductClassLan.Image = filepath.Replace("\\", "/");
                        modelProductClassLan.Update();
                    }
                }
            }
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }

        /// <summary>
        /// 修改状态
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="enable"></param>
        /// <returns></returns>
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateEnable(Int64 Id,Boolean enable)
        {
            var modelProductClass = ProductClass.FindById(Id);
            if (modelProductClass == null)
            {
                return Json(new { success = false, msg = GetResource("产品分类不存在") });
            }
            modelProductClass.Enable = enable;
            modelProductClass.Update();
            return Json(new { success = true });
        }

        [EntityAuthorize(PermissionFlags.Delete)]
        [DisplayName("删除")]
        public IActionResult Delete(String Ids)
        {
            using (var tran = ProductClass.Meta.CreateTrans())
            {
                try
                {
                    foreach (var Id in Ids.Split(","))
                    {
                        ProductClass.Delete(ProductClass._.Id == Id);
                        ProductClass.Delete(ProductClass._.ParentId == Id);
                        var list = ProductClassLan.FindAllByPId(Id.ToLong());
                        list.Delete();
                        ProductClass.Meta.Cache.Clear("");
                        ProductClassLan.Meta.Cache.Clear("");
                        tran.Commit();
                    }
                }
                catch (Exception)
                {
                    return Json(new { success = false });
                }
            }
            return Json(new { success = true });
        }
    }
}
