<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电源网</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/media.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/index.css">
    <link rel="stylesheet" href="../../modules/swiper/basic/css/idangerous.swiper.css">
    <!-- 第三方库js -->
    <script src="../../modules/layui/layui.js"></script>
    <!-- 本页私有js -->
    <script src="../../modules/swiper/basic/js/idangerous.swiper.min.js"></script>
    <!-- 综合-script -->
    <script src="../../script/index.js"></script>
    <!-- 产品分类模块 -->
    <script src="../../script/productCategory.js"></script>
    <!-- 所有页面组成部分 -->
    <script src="../../components/common.js" defer></script>
</head>

<body>
    <div class="pageBox">
         <!-- 引入头部组件 -->
     <div id="header"></div>

        <!-- 轮播图 -->
        <div class="swiper-box">

            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide"><img src="../../images/power/zhengshuhaibao.png" alt=""></div>
                    <div class="swiper-slide"><img src="../../images/power/zhengshuhaibao.png" alt=""></div>
                    <div class="swiper-slide"><img src="../../images/power/zhengshuhaibao.png" alt=""></div>
                </div>
                <!-- 如果需要分页器 -->
                <div class="pagination"></div>

                <!-- 如果需要导航按钮 -->
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>

                <!-- 如果需要滚动条 -->
                <div class="swiper-scrollbar"></div>
            </div>


        </div>

        <!-- 主体开始 -->
        <div class="mainBox0">

            <!-- 分类开始 -->
            <div class="cardBox">
                <div class="card card1">
                    <h2>产品中心</h2>
                    <p>性能优越，配套解决方案</p>
                </div>
                <div class="card card2">
                    <h2>产品选型</h2>
                    <p>快速定位满足您需求的产品</p>
                </div>
                <div class="card card3">
                    <h2>样品申请</h2>
                    <p>6000+产品免费快速申请</p>
                </div>
                <div class="card card4">
                    <h2>联系我们</h2>
                    <p>快速响应您的业务需求</p>
                </div>
            </div>
            <!-- 分类结束-->

            <!-- 动态开始 -->
            <div class="news-section">

                <!-- 企业动态 -->
                <div>
                    <p class="title">
                        <span class="wen">企业动态</span>
                        <span class="more-link">查看更多 &gt;</span>
                    </p>
                    <div class="news-card">
                        <ul class="news-list">
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">1</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">

                                    <span class="news-tag">首发</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">2</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">

                                    <span class="news-tag new">新</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">3</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                    <span class="news-tag hot">热</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index">4</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">5</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">6</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">7</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>

                        </ul>
                    </div>



                </div>
                <!-- 产品动态 -->
                <div>
                    <p class="title">
                        <span class="wen">产品动态</span>
                        <span class="more-link">查看更多 &gt;</span>
                    </p>
                    <div class="news-card blue">
                        <ul class="news-list">
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">1</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">

                                    <span class="news-tag">首发</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">2</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">

                                    <span class="news-tag new">新</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">3</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                    <span class="news-tag hot">热</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index">4</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">5</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">6</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">7</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>

                        </ul>
                    </div>

                </div>
                <!-- 技术应用 -->

                <div>
                    <p class="title">
                        <span class="wen">技术应用</span>
                        <span class="more-link">查看更多 &gt;</span>
                    </p>
                    <div class="news-card blue2">
                        <ul class="news-list">
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">1</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">

                                    <span class="news-tag">首发</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">2</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">

                                    <span class="news-tag new">新</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index" style="color: #ff4d4f;">3</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                    <span class="news-tag hot">热</span>
                                </div>
                            </li>
                            <li>
                                <span class="news-index">4</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">5</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">6</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>
                            <li>
                                <span class="news-index">7</span>
                                <span class="news-title">24G雷达模块在智能家居中的多场景应用解析</span>
                                <div class="news-tagBox">
                                </div>
                            </li>

                        </ul>
                    </div>









                </div>

            </div>
            <!-- 动态结束 -->

            <!-- 主体产品中心开始 -->
            <div class="productCenter">
                <h2>产品中心</h2>
                <div class="productContainer">
                    <div class="productBanner">
                        <img src="../../images/power/logo.png" alt="Hi-Link">
                        <a class="viewMore" href="#">查看更多</a>
                    </div>
                    <div class="productGrid">
                        <!-- AC/DC电源模块 -->
                        <div class="productItem">
                            <h3>AC/DC(2-5W)</h3>
                            <div class="myline"></div>
                            <p>智能家居/物联网隔离模块</p>
                            <img class="productImage" src="../../images/power/3w5.png" alt="">
                        </div>
                        <div class="productItem">
                            <h3>AC/DC(10-15W)</h3>
                            <div class="myline"></div>
                            <p>小功率隔离电源模块</p>
                            <img class="productImage" src="../../images/power/3w5.png" alt="">
                        </div>
                        <div class="productItem">
                            <h3>AC/DC(20-60W)</h3>
                            <div class="myline"></div>
                            <p>中大功率隔离电源模块</p>
                            <img class="productImage" src="../../images/power/3w5.png" alt=" ">
                        </div>
                        <div class="productItem">
                            <h3>机壳开关电源</h3>
                            <div class="myline"></div>
                            <p>35-350W AC/DC 壳体式开关电源</p>
                            <img class="productImage" src="../../images/power/3w5.png" alt="">
                        </div>

                        <!-- DC/DC电源模块 -->
                        <div class="productItem noBottom">
                            <h3>DC/DC定电压</h3>
                            <div class="myline"></div>
                            <p>定电压输入隔离模块</p>
                            <img class="productImage" src="../../images/power/3w5.png" alt="">
                        </div>
                        <div class="productItem noBottom">
                            <h3>DC/DC(3-15W)</h3>
                            <div class="myline"></div>
                            <p>小功率隔离稳压输出</p>
                            <img class="productImage" src="../../images/power/3w5.png" alt="">
                        </div>
                        <div class="productItem noBottom">
                            <h3>DC/DC(20-50W)</h3>
                            <div class="myline"></div>
                            <p>中大功率宽输入电压、隔离输出</p>
                            <img class="productImage" src="../../images/power/3w5.png" alt="">
                        </div>
                        <div class="productItem noBottom">
                            <h3>DC/DC非隔离模块</h3>
                            <div class="myline"></div>
                            <p>超宽电压输入 非隔离稳压输出</p>
                            <img class="productImage" src="../../images/power/3w5.png" alt="">
                        </div>
                    </div>
                </div>
            </div>
            <!-- 主体产品中心结束 -->

            <!-- 焦点专题 -->
            <div class="productCenter">
                <h2>焦点专题</h2>
                <div class="topicBox">
                    <ul class="topic">
                        <li class="li1">
                            <div class="icon"></div>
                            <p>工控</p>
                        </li>
                        <li class="li2">
                            <div class="icon"></div>
                            <p>智能电网</p>
                        </li>
                        <li class="li3">
                            <div class="icon"></div>
                            <p>轨道交通</p>
                        </li>
                        <li class="li4">
                            <div class="icon"></div>
                            <p>物联网</p>
                        </li>
                        <li class="li5">
                            <div class="icon"></div>
                            <p>汽车电子</p>
                        </li>
                    </ul>
                    <div class="topicImgBox">
                        <img class="topicImg" src="../../images/power/jiaodianzhuanti.png" alt="">
                        <div class="bg">
                            <p> 工业控制</p>
                            <p> 工控行业覆盖范围宽广，拥有不同的应用场景、复杂的工况，因此对电源性能有更高要求。 </p>
                            <p> 查看更多 &gt;</p>
                        </div>
                    </div>
                </div>

            </div>


            <!-- 主体结束 -->



            <!-- 侧边功能栏 -->
            <!-- <div class="aside">
                <div class="asideContainer">
                    <div class="asideContent">
                        <img src="../../images/icons/gouwuche.png" alt="">
                        <div>购物车</div>
                    </div>
                    <div class="asideContent">
                        <img src="../../images/icons/bangzhuzhongxin.png" alt="">
                        <div>帮助中心</div>
                    </div>
                    <div class="asideContent">
                        <img src="../../images/icons/xinxizhongxin.png" alt="">
                        <div>账号信息</div>
                    </div>
                    <div class="asideContent">
                        <img src="../../images/icons/zaixiankefu.png" alt="">
                        <div>在线客服</div>
                    </div>
                    <div class="asideContent">
                        <img src="../../images/icons/lishijilu.png" alt="">
                        <div>浏览记录</div>
                    </div>
                    <div class="asideContent" onclick="scrollToTop()">
                        <img src="../../images/icons/dingbu.png" alt="">
                        <div>返回顶部</div>
                    </div>
                </div>
            </div> -->
        </div>

       <!-- 引入底部组件 -->

    <div id="footer"></div>

</body>
<script>
    // 轮播图css
    var mySwiper = new Swiper('.swiper-container', {
        autoplay: 5000,//可选选项，自动滑动
        loop: true,//可选选项，开启循环
        // 如果需要分页器
        pagination: '.pagination',
        paginationClickable: true,
        mousewheelControl: true,
    })

    //监听鼠标hover事件
    var productCategoryBox_dom = $(".productCategoryBox");
    productCategoryBox_dom.mouseover(function (e) {
        if (e.target.getAttribute("data-select")) {
            $(e.target).siblings().attr("data-select", "false");
            e.target.setAttribute("data-select", "true");
        }
    })
    // 监听鼠标hover事件
    var mainBox1_left_dom = $(".mainBox1_left");
    mainBox1_left_dom.mouseover(function (e) {
        if (e.target.getAttribute("data-select")) {
            $(e.target).siblings().attr("data-select", "false");
            e.target.setAttribute("data-select", "true");
        }
    })
    function handleScroll() {
        // 到顶 或者到底 就隐藏
        if (isAtTop() || isAtBottom()) {
            $('.aside').fadeOut(300);
        } else {
            $('.aside').fadeIn();
        }
        // 处理滚动事件的逻辑
    }

    // 节流处理滚动事件
    const throttledScroll = throttle(handleScroll, 200);
    window.addEventListener('scroll', throttledScroll);



</script>

</html>