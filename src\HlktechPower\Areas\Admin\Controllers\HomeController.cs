﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Caching;
using NewLife.Common;
using NewLife.Cube;
using NewLife.Cube.Services;
using NewLife.Log;

using Pek;
using Pek.DsMallUI;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Webs;

using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;

using XCode;
using XCode.Membership;

namespace HlktechPower.Areas.Admin.Controllers;

/// <summary>首页</summary>
[DisplayName("首页")]
[Description("后台登录之后的首页")]
[AdminArea]
[DHMenu(100, ParentMenuName = "Home", ParentMenuDisplayName = "控制台", ParentMenuUrl = "~/{area}/Home/DashBoard", ParentMenuOrder = 100, CurrentMenuUrl = "~/{area}/Home/DashBoard", CurrentMenuName = "DashBoard", CurrentVisible = false, LastUpdate = "20241126")]
public class HomeController : PekCubeAdminControllerX {

    private readonly ICacheProvider _cacheProvider;
    private readonly IManageProvider _provider;

    /// <summary>
    /// 密码服务
    /// </summary>
    private readonly PasswordService _passwordService;

    static HomeController() => MachineInfo.RegisterAsync();

    public HomeController(ICacheProvider cacheProvider, IManageProvider manageProvider, PasswordService passwordService)
    {
        _cacheProvider = cacheProvider;
        _provider = manageProvider;
        _passwordService = passwordService;
    }

    /// <summary>
    /// 首页
    /// </summary>
    /// <returns></returns>
    [DisplayName("管理后台首页")]
    [AllowAnonymous]
    public IActionResult Index()
    {
        var user = _provider.TryLogin(HttpContext);
        if (user == null || !user.Enable || ManageProvider.User?.RoleID == 0) return RedirectToAction("Index", "Login", new { area = DHSetting.Current.AdminArea, r = Request.GetEncodedPathAndQuery() });

        ViewBag.User = _provider.Current;
        ViewBag.Config = SysConfig.Current;

        //!!! 租户切换
        var set = DHSetting.Current;
        var tenantId = Request.Query["TenantId"].ToInt(-1);
        if (tenantId >= 0 && set.EnableTenant)
        {
            // 判断租户关系
            var list = TenantUser.FindAllByUserId(user.ID);
            if (list.Any(e => e.TenantId == tenantId) || tenantId == 0)
            {
                var tenant = Tenant.FindById(tenantId);

                XTrace.WriteLine(String.Format(GetResource("用户[{0}]切换到租户[{1}/{2}]"), user, tenant?.Name ?? "系统管理员", tenant?.Code ?? "0"));

                // 切换租户，保存到Cookie
                HttpContext.SaveTenant(tenantId);

                return Redirect($"/{DHSetting.Current.AdminArea}");
            }
        }

        // 工作台页面
        var startPage = Request.GetRequestValue("page");
        if (startPage.IsNullOrWhiteSpace())
        {
            var model = XCode.Membership.Menu.FindByName("Home");
            if (model != null && model.ParentID == 0)
            {
                startPage = model.Url;
            }
        }
        if (startPage.IsNullOrEmpty()) startPage = DHSetting.Current.StartPage;
        if (!startPage.IsNullOrWhiteSpace())
            return LocalRedirect(startPage);

        ViewBag.Main = startPage;

        var module = Request.GetRequestValue("module");
        //var modules = (user as User)?.GetModules();
        ViewBag.Menus = GetMenu(module!);

        return PekView(DHSetting.Current.AllowMobileTemp, DHSetting.Current.AllowLanguageTemp);
    }

    /// <summary>
    /// 控制台
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("控制台")]
    public IActionResult DashBoard()
    {
        var menus = GetMenu();

        //_cacheProvider.Cache.Remove($"{SId}_{AdminArea.AreaName}");

        return View(menus);
    }

    /// <summary>
    /// 清除缓存
    /// </summary>
    /// <returns></returns>
    [DisplayName("清除缓存")]
    [EntityAuthorize((PermissionFlags)16)]
    public IActionResult ClearCache()
    {
        // 清除缓存数据
        _cacheProvider.Cache.Clear();

        XCode.Membership.Menu.Meta.Cache.Clear("主动清除缓存", true);

        // 清除内存
        try
        {
            GC.Collect();

            // 释放当前进程所占用的内存
            var p = Process.GetCurrentProcess();
            SetProcessWorkingSetSize(p.Handle, -1, -1);
        }
        catch (Exception ex)
        {
            XTrace.WriteException(ex);
        }

        return Json(new DResult { code = 10000, msg = GetResource("操作成功") });
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <returns></returns>
    [DisplayName("修改密码")]
    [EntityAuthorize((PermissionFlags)32)]
    [HttpGet]
    public IActionResult ModifyPw()
    {
        return View();
    }

    /// <summary>
    /// 密码提交修改
    /// </summary>
    /// <returns></returns>
    [DisplayName("修改密码")]
    [EntityAuthorize((PermissionFlags)32)]
    [HttpPost]
    public IActionResult ModifyPw(String oldPassword, String password, String repassword)
    {
        var model = ManageProvider.User;

        if (password.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("新密码不能为空") });
        }

        if (password != repassword)
        {
            return Prompt(new PromptModel { Message = GetResource("两次密码不一致") });
        }

        if (!_passwordService.Valid(repassword))
        {
            return Prompt(new PromptModel { Message = GetResource("密码太弱，要求8位起且包含数字大小写字母和符号") });
        }

        if (model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("非法用户") });
        }

        if (ManageProvider.Provider?.PasswordProvider.Verify(oldPassword, model.Password!) == false)
        {
            return Prompt(new PromptModel { Message = GetResource("旧密码错误") });
        }

        model.Password = ManageProvider.Provider?.PasswordProvider.Hash(repassword.Length == 32 ? repassword : repassword.MD5());
        (model as IEntity)!.Update();

        return Prompt(new PromptModel { Message = GetResource("修改成功"), IsOk = true });
    }

    [DllImport("kernel32.dll")]
    extern static Boolean SetProcessWorkingSetSize(IntPtr proc, Int32 min, Int32 max);
}
