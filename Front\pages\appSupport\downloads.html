<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电源网</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/jq.js"></script>
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <link rel="stylesheet" href="../../css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/downloads.css">
    <!-- 综合-script -->
    <script src="../../script/index.js" defer></script>
    <!-- 产品分类模块 -->
    <script src="../../script/productCategory.js" defer></script>
    <!-- 所有页面组成部分 -->
    <script src="../../components/common.js" defer></script>
    <script src="../../components/mySidebar.js" defer></script>
    <!-- 单页面组成部分 -->
    <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
    <!-- 引入头部组件 -->
    <div id="header"></div>

    <div class="main"> 
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">应用支持</div>
        </div>
        <div class="dataDown">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            </video>
            
            <div class="dataDown-right">
               <div class="search">
                   <div class="h3">资料下载</div>
                   <div class="layui-input-group">
                    <input type="text" placeholder="" class="layui-input">
                    <div class="layui-input-split layui-input-suffix">
                      <i class="iconfont icon-search"></i>&nbsp;<span class="wen">搜索</span>
                    </div>
                  </div>
               </div>
               <div class="layui-tab layui-tab-brief">
                <ul class="layui-tab-title">
                  <li class="layui-this">产品目录</li>
                  <li>应用笔记</li>
                  <li>配置文件</li>
                  <li>常见故障分析</li>
                </ul>
                <div class="layui-tab-content">
                  <div class="layui-tab-item layui-show">
                    <ul class="item">
                        <li>
                            <div>标题</div>
                            <div>下载</div>
                        </li>
                        <li>
                            <div>电源模块产品目录册(2025)</div>
                            <div><img src="../../images/power/xiazai.png" alt=""></div>
                        </li>
                        <li>
                            <div>产品目录册(2025)</div>
                            <div><img src="../../images/power/xiazai.png" alt=""></div>
                        </li>
                    </ul>
                  </div>
                  <div class="layui-tab-item">
                    <ul class="item">
                        <li>
                            <div>标题2</div>
                            <div>下载</div>
                        </li>
                        <li>
                            <div>电源模块产品目录册(2025)</div>
                            <div><img src="../../images/power/xiazai.png" alt=""></div>
                        </li>
                        <li>
                            <div>产品目录册(2025)</div>
                            <div><img src="../../images/power/xiazai.png" alt=""></div>
                        </li>
                    </ul>
                  </div>
                  <div class="layui-tab-item">
                    <ul class="item">
                        <li>
                            <div>标题3</div>
                            <div>下载</div>
                        </li>
                        <li>
                            <div>电源模块产品目录册(2025)</div>
                            <div><img src="../../images/power/xiazai.png" alt=""></div>
                        </li>
                    </ul>
                  </div>
                  <div class="layui-tab-item">
                    <ul class="item">
                        <li>
                            <div>标题4</div>
                            <div>下载</div>
                        </li>
                        <li>
                            <div>电源模块产品目录册(2025)</div>
                            <div><img src="../../images/power/xiazai.png" alt=""></div>
                        </li>
                        <li>
                            <div>电源模块产品目录册(2025)</div>
                            <div><img src="../../images/power/xiazai.png" alt=""></div>
                        </li>
                    </ul>
                  </div>

                </div>
              </div>


               </div>

        </div>
    </div>

    <div class="bug"></div>

    <!-- 引入底部组件 -->
    <div id="footer"></div>
</body>
<script>
const data = [
    {  text: '应用支持', link: '#' },  // 标题
    {  text: '焦点专题', link: '#' },
    {  text: '资料下载', link: './downloads.html' },
    {  text: '应用视频', link: './videos.html' },
    {  text: '常见问题', link: './faq.html' },
    {  text: '样品申请', link: './sample.html' },
    {  text: '成品检测报告', link: './reports.html' }
];

</script>

</html>